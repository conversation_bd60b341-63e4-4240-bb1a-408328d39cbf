# UI 组件国际化指南

本文档介绍如何在项目中实现公共 UI 组件的国际化。

## 📋 概述

项目采用了自动检测的国际化架构：

1. **UI 组件层** (`packages/ui/`) - 内置翻译字典，自动检测语言环境
2. **语言检测** - 通过 HTML lang 属性和浏览器语言自动切换
3. **应用层** (`apps/web/`, `apps/management/`) - 直接使用 UI 组件，无需额外配置

## 🏗️ 架构设计

### 内置翻译字典

UI 组件内置了中英文翻译字典：

```typescript
const translations = {
  en: {
    pagination: {
      previous: "Previous",
      next: "Next",
      morePages: "More pages",
      showingItems: ({ start, end, total }) => `Showing ${start} - ${end} of ${total} items`,
      itemsPerPage: "Items per page",
      items: "items",
    },
    common: {
      loading: "Loading...",
      cancel: "Cancel",
      confirm: "Confirm",
      // ...
    }
  },
  zh: {
    pagination: {
      previous: "上一页",
      next: "下一页",
      morePages: "更多页面",
      showingItems: ({ start, end, total }) => `显示第 ${start} - ${end} 条，共 ${total} 条`,
      itemsPerPage: "每页",
      items: "条",
    },
    common: {
      loading: "加载中...",
      cancel: "取消",
      confirm: "确认",
      // ...
    }
  }
};
```

### 自动语言检测

组件会自动检测当前语言环境：

1. **HTML lang 属性** - 检查 `document.documentElement.lang`
2. **浏览器语言** - 检查 `navigator.language`
3. **默认语言** - 如果检测失败，默认使用英文

### 动态语言切换

组件会监听 HTML lang 属性的变化，实现动态语言切换：

```typescript
useEffect(() => {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'lang') {
        const newLocale = detectLocale();
        if (newLocale !== locale) {
          setLocale(newLocale);
        }
      }
    });
  });

  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['lang'],
  });

  return () => observer.disconnect();
}, [locale]);
```

## 🔧 使用方法

### 1. 直接使用组件

无需任何配置，直接使用 UI 组件：

```typescript
import { Pagination } from "@ragtop-web/ui/components/pagination";

export function MyComponent() {
  return (
    <Pagination
      pageCount={10}
      currentPage={1}
      onPageChange={setPage}
      // 组件会自动使用当前语言的翻译
    />
  );
}
```

### 2. 自定义翻译文本

如果需要覆盖默认翻译，可以传入自定义文本：

```typescript
import { Pagination } from "@ragtop-web/ui/components/pagination";
import { usePaginationTranslations } from "@ragtop-web/ui/hooks/use-ui-translations";

export function MyComponent() {
  const translations = usePaginationTranslations();
  
  return (
    <Pagination
      pageCount={10}
      currentPage={1}
      onPageChange={setPage}
      translations={{
        ...translations,
        previous: "自定义上一页",
        next: "自定义下一页",
      }}
    />
  );
}
```

### 3. 使用翻译 Hook

可以直接使用翻译 hook 获取翻译文本：

```typescript
import { useUiTranslations } from "@ragtop-web/ui/hooks/use-ui-translations";

export function MyComponent() {
  const { common, pagination } = useUiTranslations();
  
  return (
    <div>
      <button>{common.save}</button>
      <span>{pagination.previous}</span>
    </div>
  );
}
```

## 📦 支持的组件

### Pagination 组件

自动国际化的分页组件，支持：
- 上一页/下一页按钮
- 页面信息显示
- 每页大小选择器

### ConfirmDialog 组件

自动国际化的确认对话框，支持：
- 确认/取消按钮
- 加载状态文本

### Dialog 组件

自动国际化的对话框，支持：
- 关闭按钮的屏幕阅读器文本

## 🧪 测试

访问 `/test-ui-i18n` 页面可以测试 UI 组件的国际化功能。

## 🔄 语言切换

要实现语言切换，只需修改 HTML lang 属性：

```typescript
// 切换到中文
document.documentElement.lang = 'zh';

// 切换到英文
document.documentElement.lang = 'en';
```

组件会自动检测变化并更新显示的文本。

## 📝 最佳实践

1. **保持简洁** - UI 组件自动处理国际化，无需额外配置
2. **测试覆盖** - 确保所有语言版本都经过测试
3. **性能考虑** - 翻译检测只在组件挂载和语言变化时执行
4. **扩展性** - 可以轻松添加新的语言支持

## 🚀 扩展指南

要添加新语言支持：

1. 在 `packages/ui/src/hooks/use-ui-translations.ts` 中添加新语言的翻译字典
2. 更新 `detectLocale` 函数以支持新语言检测
3. 更新类型定义以包含新语言

这种架构确保了 UI 组件的易用性，同时提供了完整的国际化支持。
