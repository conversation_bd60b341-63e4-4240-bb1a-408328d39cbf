#!/usr/bin/env node

/**
 * i18n 配置测试脚本
 *
 * 用于验证：
 * 1. 翻译文件是否存在
 * 2. 翻译键是否匹配
 * 3. 配置文件是否正确
 */

import { existsSync, readFileSync } from "fs";
import { dirname, join } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, "..");

console.log("🔍 检查 i18n 配置...\n");

// 检查翻译文件
const localesPath = join(rootDir, "packages/i18n/dictionaries");
const languages = ["en", "zh"];

console.log("📁 检查翻译文件:");
languages.forEach((lang) => {
  const filePath = join(localesPath, `${lang}.json`);
  if (existsSync(filePath)) {
    console.log(`  ✅ ${lang}.json 存在`);
    try {
      const content = JSON.parse(readFileSync(filePath, "utf8"));
      console.log(`     - 包含 ${Object.keys(content).length} 个顶级键`);
    } catch (error) {
      console.log(`     ❌ JSON 格式错误: ${error.message}`);
    }
  } else {
    console.log(`  ❌ ${lang}.json 不存在`);
  }
});

// 检查配置文件
console.log("\n⚙️  检查配置文件:");

const configFiles = [
  ".vscode/settings.json",
  ".vscode/i18n-ally.config.json",
  "project.inlang/settings.json",
];

configFiles.forEach((configFile) => {
  const filePath = join(rootDir, configFile);
  if (existsSync(filePath)) {
    console.log(`  ✅ ${configFile} 存在`);
  } else {
    console.log(`  ❌ ${configFile} 不存在`);
  }
});

// 检查测试键
console.log("\n🧪 检查测试翻译键:");
const testKeys = [
  "web.test.title",
  "web.test.description",
  "web.test.button.save",
  "web.test.button.cancel",
];

languages.forEach((lang) => {
  const filePath = join(localesPath, `${lang}.json`);
  if (existsSync(filePath)) {
    try {
      const content = JSON.parse(readFileSync(filePath, "utf8"));
      console.log(`\n  ${lang.toUpperCase()} 翻译:`);

      testKeys.forEach((key) => {
        const keys = key.split(".");
        let value = content;

        for (const k of keys) {
          value = value?.[k];
        }

        if (value) {
          console.log(`    ✅ ${key}: "${value}"`);
        } else {
          console.log(`    ❌ ${key}: 缺失`);
        }
      });
    } catch (error) {
      console.log(`    ❌ 无法解析 ${lang}.json`);
    }
  }
});

// 检查空值翻译
console.log("\n🔍 检查空值翻译:");

function findEmptyValues(obj, prefix = "", emptyKeys = []) {
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;

    if (typeof value === "object" && value !== null) {
      findEmptyValues(value, fullKey, emptyKeys);
    } else if (value === "" || value === null || value === undefined) {
      emptyKeys.push(fullKey);
    }
  }
  return emptyKeys;
}

languages.forEach((lang) => {
  const filePath = join(localesPath, `${lang}.json`);
  if (existsSync(filePath)) {
    try {
      const content = JSON.parse(readFileSync(filePath, "utf8"));
      const emptyKeys = findEmptyValues(content);

      console.log(`\n  ${lang.toUpperCase()} 空值检查:`);
      if (emptyKeys.length === 0) {
        console.log(`    ✅ 没有发现空值`);
      } else {
        console.log(`    ❌ 发现 ${emptyKeys.length} 个空值:`);
        emptyKeys.slice(0, 10).forEach((key) => {
          console.log(`      - ${key}`);
        });
        if (emptyKeys.length > 10) {
          console.log(`      ... 还有 ${emptyKeys.length - 10} 个空值`);
        }
      }
    } catch (error) {
      console.log(`    ❌ 无法解析 ${lang}.json`);
    }
  }
});

console.log("\n✨ i18n 配置检查完成!");
console.log("\n💡 使用提示:");
console.log("1. 确保安装了 i18n-ally VSCode 扩展");
console.log("2. 重启 VSCode 以应用新配置");
console.log("3. 访问 /test-i18n 页面测试功能");
console.log("4. 在 VSCode 中点击翻译键应该能跳转到对应文件");
console.log("5. 如果发现空值，请在 VSCode 中使用 i18n-ally 的翻译功能填充");
