#!/usr/bin/env node

/**
 * 修复空翻译值脚本
 * 
 * 用于：
 * 1. 检查翻译文件中的空值
 * 2. 从中文翻译复制到英文翻译（如果中文有值）
 * 3. 为空值提供默认的英文翻译
 */

import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

console.log('🔧 修复空翻译值...\n');

const localesPath = join(rootDir, 'packages/i18n/dictionaries');
const enPath = join(localesPath, 'en.json');
const zhPath = join(localesPath, 'zh.json');

if (!existsSync(enPath) || !existsSync(zhPath)) {
  console.log('❌ 翻译文件不存在');
  process.exit(1);
}

// 读取翻译文件
const enContent = JSON.parse(readFileSync(enPath, 'utf8'));
const zhContent = JSON.parse(readFileSync(zhPath, 'utf8'));

// 查找空值
function findEmptyValues(obj, prefix = '', emptyKeys = []) {
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'object' && value !== null) {
      findEmptyValues(value, fullKey, emptyKeys);
    } else if (value === '' || value === null || value === undefined) {
      emptyKeys.push(fullKey);
    }
  }
  return emptyKeys;
}

// 获取嵌套对象的值
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

// 设置嵌套对象的值
function setNestedValue(obj, path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((current, key) => {
    if (!current[key]) current[key] = {};
    return current[key];
  }, obj);
  target[lastKey] = value;
}

// 默认翻译映射
const defaultTranslations = {
  'management.models.searchModelName': 'Search Model Name',
  'management.models.noResults': 'No Results',
  'management.models.useModelName': 'Use Model Name',
  'management.models.baseUrl': 'Base URL',
  'management.models.apiVersionPlaceholder': 'Enter API Version',
  'management.models.selectMaxTokens': 'Select Max Tokens',
  'management.models.enableThinking': 'Enable Thinking',
  'models.apiKeyRequired': 'API Key is required',
  'models.modelPurposeRequired': 'Model purpose is required',
  'models.apiVersionRequired': 'API version is required',
  'models.baseUrlRequired': 'Base URL is required',
  'models.maxTokensRequired': 'Max tokens is required',
  'models.modelAliasRequired': 'Model alias is required',
  'models.modelNameRequired': 'Model name is required',
  'models.modelTypeRequired': 'Model type is required'
};

const emptyKeys = findEmptyValues(enContent);
console.log(`📋 发现 ${emptyKeys.length} 个空值需要修复:`);

let fixedCount = 0;

emptyKeys.forEach(key => {
  console.log(`\n🔍 处理键: ${key}`);
  
  // 首先尝试从中文翻译获取值
  const zhValue = getNestedValue(zhContent, key);
  
  if (zhValue && zhValue !== '') {
    console.log(`  📝 从中文复制: "${zhValue}"`);
    setNestedValue(enContent, key, zhValue);
    fixedCount++;
  } else if (defaultTranslations[key]) {
    console.log(`  🔤 使用默认翻译: "${defaultTranslations[key]}"`);
    setNestedValue(enContent, key, defaultTranslations[key]);
    fixedCount++;
  } else {
    // 生成基于键名的默认翻译
    const keyParts = key.split('.');
    const lastPart = keyParts[keyParts.length - 1];
    const defaultValue = lastPart
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();
    
    console.log(`  🤖 生成默认翻译: "${defaultValue}"`);
    setNestedValue(enContent, key, defaultValue);
    fixedCount++;
  }
});

// 保存修复后的文件
if (fixedCount > 0) {
  writeFileSync(enPath, JSON.stringify(enContent, null, 2) + '\n', 'utf8');
  console.log(`\n✅ 修复完成！共修复了 ${fixedCount} 个空值`);
  console.log(`📁 已更新文件: ${enPath}`);
} else {
  console.log('\n✅ 没有需要修复的空值');
}

console.log('\n💡 建议:');
console.log('1. 重启 VSCode 以刷新 i18n-ally 缓存');
console.log('2. 检查修复后的翻译是否合适');
console.log('3. 如需调整翻译，请直接编辑翻译文件');
