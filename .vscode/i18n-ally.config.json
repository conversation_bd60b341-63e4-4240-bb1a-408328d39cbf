{"localesPaths": ["packages/i18n/dictionaries"], "pathMatcher": "{locale}.json", "keystyle": "nested", "namespace": false, "enabledParsers": ["json"], "sourceLanguage": "zh", "displayLanguage": "zh", "enabledFrameworks": ["next-intl"], "extract": {"keygenStyle": "kebab-case", "keyMaxLength": 50}, "annotations": true, "review": {"enabled": true, "gutters": true}, "editor": {"preferEditor": true}, "translate": {"engines": ["google", "deepl"], "fallbackMissing": true}}