"use client";

import { ReactNode, useEffect, useRef } from "react";
import { useCommonTranslations } from "../hooks/use-ui-translations";

import { Button } from "./button.tsx";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "./dialog.tsx";

interface ConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string | ReactNode;
  onConfirm: (e: React.MouseEvent) => void;
  onCancel?: () => void;
  confirmText?: string;
  cancelText?: string;
  loadingText?: string;
  variant?: "default" | "destructive";
  loading?: boolean;
  disabled?: boolean;
}

export const ConfirmDialog = ({
  open,
  onOpenChange,
  title,
  description,
  onConfirm,
  onCancel,
  confirmText,
  cancelText,
  loadingText,
  variant = "default",
  loading = false,
  disabled = false,
}: ConfirmDialogProps) => {
  const firstButtonRef = useRef<HTMLButtonElement>(null);
  const t = useCommonTranslations();

  // 使用传入的翻译文本或默认翻译
  const finalConfirmText = confirmText || t.confirm;
  const finalCancelText = cancelText || t.cancel;
  const finalLoadingText = loadingText || t.processing;

  // 当对话框打开时，聚焦到第一个按钮以确保焦点转移
  useEffect(() => {
    if (open && firstButtonRef.current) {
      // 使用 setTimeout 确保在 DOM 更新后聚焦
      setTimeout(() => {
        firstButtonRef.current?.focus();
      }, 0);
    }
  }, [open]);

  const handleCancel = () => {
    onCancel?.();
    onOpenChange(false);
  };

  const handleConfirm = (e: React.MouseEvent) => {
    onConfirm(e);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader className="pb-0">
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            ref={firstButtonRef}
            variant="outline"
            onClick={handleCancel}
            disabled={disabled || loading}
          >
            {finalCancelText}
          </Button>
          <Button
            variant={variant === "destructive" ? "destructive" : "default"}
            onClick={handleConfirm}
            disabled={disabled || loading}
          >
            {loading ? finalLoadingText : finalConfirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
