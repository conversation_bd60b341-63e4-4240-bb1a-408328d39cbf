"use client";

import { ReactNode } from "react";
import { 
  UiTranslationsContext, 
  UiTranslations, 
  defaultUiTranslations 
} from "../hooks/use-ui-translations";

interface UiTranslationsProviderProps {
  children: ReactNode;
  translations?: UiTranslations;
}

/**
 * UI 翻译提供者组件
 * 
 * 为所有子组件提供翻译上下文
 */
export function UiTranslationsProvider({ 
  children, 
  translations = defaultUiTranslations 
}: UiTranslationsProviderProps) {
  return (
    <UiTranslationsContext.Provider value={translations}>
      {children}
    </UiTranslationsContext.Provider>
  );
}
