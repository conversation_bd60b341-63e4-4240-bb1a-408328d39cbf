"use client";

import { <PERSON><PERSON>ron<PERSON>eft, ChevronR<PERSON>, MoreHorizontal } from "lucide-react";
import * as React from "react";

import { PaginationTranslations, usePaginationTranslations } from "../hooks/use-ui-translations";
import { cn } from "../lib/utils";
import { ButtonProps, buttonVariants } from "./button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./select";

const PaginationRoot = ({
  className,
  ...props
}: React.ComponentProps<"nav"> & {
  className?: string;
}) => (
  <nav
    role="navigation"
    aria-label="pagination"
    className={cn("mx-auto flex w-full justify-center", className)}
    {...props}
  />
);

const PaginationContent = React.forwardRef<HTMLUListElement, React.ComponentProps<"ul">>(
  ({ className, ...props }, ref) => (
    <ul ref={ref} className={cn("flex flex-row items-center gap-1", className)} {...props} />
  )
);

PaginationContent.displayName = "PaginationContent";

const PaginationItem = React.forwardRef<HTMLLIElement, React.ComponentProps<"li">>(
  ({ className, ...props }, ref) => <li ref={ref} className={cn("", className)} {...props} />
);

PaginationItem.displayName = "PaginationItem";

type PaginationLinkProps = {
  isActive?: boolean;
  disabled?: boolean;
} & Pick<ButtonProps, "size"> &
  React.ComponentProps<"button">;

const PaginationLink = ({
  className,
  isActive,
  disabled,
  size = "icon",
  ...props
}: PaginationLinkProps) => (
  <button
    aria-current={isActive ? "page" : undefined}
    disabled={disabled}
    className={cn(
      buttonVariants({
        variant: isActive ? "outline" : "ghost",
        size,
      }),
      disabled && "pointer-events-none opacity-50",
      className
    )}
    {...props}
  />
);

PaginationLink.displayName = "PaginationLink";

const PaginationPrevious = ({
  className,
  children = "Previous",
  ...props
}: React.ComponentProps<typeof PaginationLink> & { children?: React.ReactNode }) => {
  return (
    <PaginationLink
      aria-label="Go to previous page"
      className={cn("gap-1 pl-2.5", className)}
      {...props}
    >
      <ChevronLeft className="h-4 w-4" />
      <span>{children}</span>
    </PaginationLink>
  );
};

PaginationPrevious.displayName = "PaginationPrevious";

const PaginationNext = ({
  className,
  children = "Next",
  ...props
}: React.ComponentProps<typeof PaginationLink> & { children?: React.ReactNode }) => {
  return (
    <PaginationLink
      aria-label="Go to next page"
      className={cn("gap-1 pr-2.5", className)}
      {...props}
    >
      <span>{children}</span>
      <ChevronRight className="h-4 w-4" />
    </PaginationLink>
  );
};

PaginationNext.displayName = "PaginationNext";

const PaginationEllipsis = ({
  className,
  srText = "More pages",
  ...props
}: React.ComponentProps<"span"> & { srText?: string }) => {
  return (
    <span
      aria-hidden
      className={cn("flex h-9 w-9 items-center justify-center", className)}
      {...props}
    >
      <MoreHorizontal className="h-4 w-4" />
      <span className="sr-only">{srText}</span>
    </span>
  );
};

PaginationEllipsis.displayName = "PaginationEllipsis";

interface PaginationProps {
  pageCount: number;
  currentPage: number;
  onPageChange: (page: number) => void;
  siblingCount?: number;
  /** 总条目数 */
  total?: number;
  /** 每页大小 */
  pageSize?: number;
  /** 每页大小变化回调 */
  onPageSizeChange?: (pageSize: number) => void;
  /** 可选的每页大小选项 */
  pageSizeOptions?: number[];
  /** 是否显示总数信息 */
  showTotal?: boolean;
  /** 是否显示每页大小选择器 */
  showPageSizeSelector?: boolean;
  /** 翻译文本 */
  translations: PaginationTranslations;
}

function usePagination({
  pageCount,
  currentPage,
  siblingCount = 1,
}: {
  pageCount: number;
  currentPage: number;
  siblingCount?: number;
}) {
  const paginationRange = React.useMemo(() => {
    // 计算要显示的页码范围
    const totalPageNumbers = siblingCount + 5; // 首页 + 尾页 + 当前页 + 2个省略号

    // 如果页数少于要显示的页码数，则全部显示
    if (totalPageNumbers >= pageCount) {
      return Array.from({ length: pageCount }, (_, i) => i + 1);
    }

    // 计算左右兄弟页的索引
    const leftSiblingIndex = Math.max(currentPage - siblingCount, 1);
    const rightSiblingIndex = Math.min(currentPage + siblingCount, pageCount);

    // 是否显示左右省略号
    const shouldShowLeftDots = leftSiblingIndex > 2;
    const shouldShowRightDots = rightSiblingIndex < pageCount - 1;

    const firstPageIndex = 1;
    const lastPageIndex = pageCount;

    // 只显示右省略号
    if (!shouldShowLeftDots && shouldShowRightDots) {
      const leftItemCount = 3 + 2 * siblingCount;
      const leftRange = Array.from({ length: leftItemCount }, (_, i) => i + 1);

      return [...leftRange, "...", lastPageIndex];
    }

    // 只显示左省略号
    if (shouldShowLeftDots && !shouldShowRightDots) {
      const rightItemCount = 3 + 2 * siblingCount;
      const rightRange = Array.from(
        { length: rightItemCount },
        (_, i) => pageCount - rightItemCount + i + 1
      );

      return [firstPageIndex, "...", ...rightRange];
    }

    // 显示左右省略号
    if (shouldShowLeftDots && shouldShowRightDots) {
      const middleRange = Array.from(
        { length: rightSiblingIndex - leftSiblingIndex + 1 },
        (_, i) => leftSiblingIndex + i
      );

      return [firstPageIndex, "...", ...middleRange, "...", lastPageIndex];
    }

    return [];
  }, [pageCount, currentPage, siblingCount]);

  return paginationRange;
}

export function Pagination({
  pageCount,
  currentPage,
  onPageChange,
  siblingCount = 1,
  total,
  pageSize = 10,
  onPageSizeChange,
  pageSizeOptions = [10, 20, 50, 100],
  showTotal = true,
  showPageSizeSelector = true,
  translations,
}: PaginationProps) {
  // 使用传入的翻译或自动检测的翻译
  const autoTranslations = usePaginationTranslations();
  const t = translations || autoTranslations;

  const paginationRange = usePagination({
    pageCount,
    currentPage,
    siblingCount,
  });

  // 如果只有一页且不显示总数和页面大小选择器，则不显示分页
  if (pageCount <= 1 && !showTotal && !showPageSizeSelector) {
    return null;
  }

  const onNext = () => {
    if (currentPage < pageCount) {
      onPageChange(currentPage + 1);
    }
  };

  const onPrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  // 计算当前页显示的条目范围
  const startItem = total ? Math.min((currentPage - 1) * pageSize + 1, total) : 0;
  const endItem = total ? Math.min(currentPage * pageSize, total) : 0;

  return (
    <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
      {/* 总数信息 */}
      {showTotal && total !== undefined && (
        <div className="text-muted-foreground text-sm">
          {t.showingItems({ start: startItem, end: endItem, total })}
        </div>
      )}

      {/* 分页控件 */}
      {pageCount > 1 && (
        <nav className="flex justify-center">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                size={"default"}
                onClick={onPrevious}
                disabled={currentPage === 1}
              >
                {t.previous}
              </PaginationPrevious>
            </PaginationItem>
            {paginationRange.map((pageNumber, i) =>
              pageNumber === "..." ? (
                <PaginationItem key={`ellipsis-${i}`}>
                  <PaginationEllipsis srText={t.morePages} />
                </PaginationItem>
              ) : (
                <PaginationItem key={pageNumber}>
                  <PaginationLink
                    size={"default"}
                    isActive={pageNumber === currentPage}
                    onClick={() => onPageChange(pageNumber as number)}
                  >
                    {pageNumber}
                  </PaginationLink>
                </PaginationItem>
              )
            )}
            <PaginationItem>
              <PaginationNext
                size={"default"}
                onClick={onNext}
                disabled={currentPage === pageCount}
              >
                {t.next}
              </PaginationNext>
            </PaginationItem>
          </PaginationContent>
        </nav>
      )}

      {/* 每页大小选择器 */}
      {showPageSizeSelector && onPageSizeChange && (
        <div className="flex items-center gap-2">
          <span className="text-muted-foreground text-sm">{t.itemsPerPage}</span>
          <Select
            value={pageSize.toString()}
            onValueChange={(value) => onPageSizeChange(Number(value))}
          >
            <SelectTrigger size="sm" className="w-fit">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {pageSizeOptions.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <span className="text-muted-foreground text-sm">{t.items}</span>
        </div>
      )}
    </div>
  );
}

export {
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationRoot,
};
