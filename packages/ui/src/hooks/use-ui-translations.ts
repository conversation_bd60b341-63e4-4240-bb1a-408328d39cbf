"use client";

import { useEffect, useState } from "react";

/**
 * UI 组件国际化类型定义
 *
 * 定义 UI 组件所需的翻译文本接口
 */

/**
 * 分页组件翻译文本接口
 */
export interface PaginationTranslations {
  previous: string;
  next: string;
  morePages: string;
  showingItems: (params: { start: number; end: number; total: number }) => string;
  itemsPerPage: string;
  items: string;
}

/**
 * 通用文本翻译接口
 */
export interface CommonTranslations {
  loading: string;
  error: string;
  success: string;
  cancel: string;
  confirm: string;
  save: string;
  delete: string;
  edit: string;
  add: string;
  search: string;
  close: string;
  processing: string;
}

/**
 * 对话框翻译接口
 */
export interface DialogTranslations {
  close: string;
}

/**
 * 翻译字典
 */
const translations = {
  en: {
    pagination: {
      previous: "Previous",
      next: "Next",
      morePages: "More pages",
      showingItems: ({ start, end, total }: { start: number; end: number; total: number }) =>
        `Showing ${start} - ${end} of ${total} items`,
      itemsPerPage: "Items per page",
      items: "items",
    },
    common: {
      loading: "Loading...",
      error: "Error",
      success: "Success",
      cancel: "Cancel",
      confirm: "Confirm",
      save: "Save",
      delete: "Delete",
      edit: "Edit",
      add: "Add",
      search: "Search",
      close: "Close",
      processing: "Processing...",
    },
    dialog: {
      close: "Close",
    },
  },
  zh: {
    pagination: {
      previous: "上一页",
      next: "下一页",
      morePages: "更多页面",
      showingItems: ({ start, end, total }: { start: number; end: number; total: number }) =>
        `显示第 ${start} - ${end} 条，共 ${total} 条`,
      itemsPerPage: "每页",
      items: "条",
    },
    common: {
      loading: "加载中...",
      error: "错误",
      success: "成功",
      cancel: "取消",
      confirm: "确认",
      save: "保存",
      delete: "删除",
      edit: "编辑",
      add: "添加",
      search: "搜索",
      close: "关闭",
      processing: "处理中...",
    },
    dialog: {
      close: "关闭",
    },
  },
} as const;

/**
 * 检测当前语言环境
 */
function detectLocale(): "en" | "zh" {
  if (typeof window === "undefined") {
    return "en"; // SSR 默认英文
  }

  // 尝试从 HTML lang 属性获取
  const htmlLang = document.documentElement.lang;
  if (htmlLang.startsWith("zh")) {
    return "zh";
  }
  if (htmlLang.startsWith("en")) {
    return "en";
  }

  // 尝试从浏览器语言获取
  const browserLang = navigator.language || navigator.languages?.[0] || "en";
  if (browserLang.startsWith("zh")) {
    return "zh";
  }

  return "en";
}

/**
 * 使用 UI 翻译的 hook
 */
export function useUiTranslations() {
  const [locale, setLocale] = useState<"en" | "zh">(() => detectLocale());

  useEffect(() => {
    // 监听语言变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === "attributes" && mutation.attributeName === "lang") {
          const newLocale = detectLocale();
          if (newLocale !== locale) {
            setLocale(newLocale);
          }
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["lang"],
    });

    return () => observer.disconnect();
  }, [locale]);

  return translations[locale];
}

/**
 * 分页专用的国际化 hook
 */
export function usePaginationTranslations(): PaginationTranslations {
  const { pagination } = useUiTranslations();
  return pagination;
}

/**
 * 通用文本专用的国际化 hook
 */
export function useCommonTranslations(): CommonTranslations {
  const { common } = useUiTranslations();
  return common;
}

/**
 * 对话框专用的国际化 hook
 */
export function useDialogTranslations(): DialogTranslations {
  const { dialog } = useUiTranslations();
  return dialog;
}
