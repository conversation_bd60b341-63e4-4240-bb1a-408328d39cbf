"use client";

import { createContext, useContext } from "react";

/**
 * UI 组件国际化类型定义
 *
 * 定义 UI 组件所需的翻译文本接口
 */

/**
 * 分页组件翻译文本接口
 */
export interface PaginationTranslations {
  previous: string;
  next: string;
  morePages: string;
  showingItems: (params: { start: number; end: number; total: number }) => string;
  itemsPerPage: string;
  items: string;
}

/**
 * 通用文本翻译接口
 */
export interface CommonTranslations {
  loading: string;
  error: string;
  success: string;
  cancel: string;
  confirm: string;
  save: string;
  delete: string;
  edit: string;
  add: string;
  search: string;
  close: string;
  processing: string;
}

/**
 * 对话框翻译接口
 */
export interface DialogTranslations {
  close: string;
}

/**
 * UI 翻译上下文接口
 */
export interface UiTranslations {
  pagination: PaginationTranslations;
  common: CommonTranslations;
  dialog: DialogTranslations;
}

/**
 * 默认的英文翻译
 */
export const defaultPaginationTranslations: PaginationTranslations = {
  previous: "Previous",
  next: "Next",
  morePages: "More pages",
  showingItems: ({ start, end, total }) => `Showing ${start} - ${end} of ${total} items`,
  itemsPerPage: "Items per page",
  items: "items",
};

export const defaultCommonTranslations: CommonTranslations = {
  loading: "Loading...",
  error: "Error",
  success: "Success",
  cancel: "Cancel",
  confirm: "Confirm",
  save: "Save",
  delete: "Delete",
  edit: "Edit",
  add: "Add",
  search: "Search",
  close: "Close",
  processing: "Processing...",
};

export const defaultDialogTranslations: DialogTranslations = {
  close: "Close",
};

/**
 * 默认的中文翻译
 */
export const zhPaginationTranslations: PaginationTranslations = {
  previous: "上一页",
  next: "下一页",
  morePages: "更多页面",
  showingItems: ({ start, end, total }) => `显示第 ${start} - ${end} 条，共 ${total} 条`,
  itemsPerPage: "每页",
  items: "条",
};

export const zhCommonTranslations: CommonTranslations = {
  loading: "加载中...",
  error: "错误",
  success: "成功",
  cancel: "取消",
  confirm: "确认",
  save: "保存",
  delete: "删除",
  edit: "编辑",
  add: "添加",
  search: "搜索",
  close: "关闭",
  processing: "处理中...",
};

export const zhDialogTranslations: DialogTranslations = {
  close: "关闭",
};

/**
 * 默认翻译集合
 */
export const defaultUiTranslations: UiTranslations = {
  pagination: defaultPaginationTranslations,
  common: defaultCommonTranslations,
  dialog: defaultDialogTranslations,
};

export const zhUiTranslations: UiTranslations = {
  pagination: zhPaginationTranslations,
  common: zhCommonTranslations,
  dialog: zhDialogTranslations,
};

/**
 * UI 翻译上下文
 */
export const UiTranslationsContext = createContext<UiTranslations>(defaultUiTranslations);

/**
 * 使用 UI 翻译的 hook
 */
export function useUiTranslations(): UiTranslations {
  return useContext(UiTranslationsContext);
}

/**
 * 分页专用的国际化 hook
 */
export function usePaginationTranslations(): PaginationTranslations {
  const { pagination } = useUiTranslations();
  return pagination;
}

/**
 * 通用文本专用的国际化 hook
 */
export function useCommonTranslations(): CommonTranslations {
  const { common } = useUiTranslations();
  return common;
}

/**
 * 对话框专用的国际化 hook
 */
export function useDialogTranslations(): DialogTranslations {
  const { dialog } = useUiTranslations();
  return dialog;
}
