/**
 * UI 组件国际化类型定义
 *
 * 定义 UI 组件所需的翻译文本接口
 */

/**
 * 分页组件翻译文本接口
 */
export interface PaginationTranslations {
  previous: string;
  next: string;
  morePages: string;
  showingItems: (params: { start: number; end: number; total: number }) => string;
  itemsPerPage: string;
  items: string;
}

/**
 * 通用文本翻译接口
 */
export interface CommonTranslations {
  loading: string;
  error: string;
  success: string;
  cancel: string;
  confirm: string;
  save: string;
  delete: string;
  edit: string;
  add: string;
  search: string;
  close: string;
  processing: string;
}

/**
 * 对话框翻译接口
 */
export interface DialogTranslations {
  close: string;
}

/**
 * 默认的英文翻译
 */
export const defaultPaginationTranslations: PaginationTranslations = {
  previous: "Previous",
  next: "Next",
  morePages: "More pages",
  showingItems: ({ start, end, total }) => `Showing ${start} - ${end} of ${total} items`,
  itemsPerPage: "Items per page",
  items: "items",
};

export const defaultCommonTranslations: CommonTranslations = {
  loading: "Loading...",
  error: "Error",
  success: "Success",
  cancel: "Cancel",
  confirm: "Confirm",
  save: "Save",
  delete: "Delete",
  edit: "Edit",
  add: "Add",
  search: "Search",
  close: "Close",
  processing: "Processing...",
};

export const defaultDialogTranslations: DialogTranslations = {
  close: "Close",
};
