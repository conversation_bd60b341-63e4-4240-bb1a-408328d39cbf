"use client";

import { AgentDetailsParams, Agents, Scope, Type } from "@/service/agent-service";
import { Badge } from "@ragtop-web/ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@ragtop-web/ui/components/card";
import { RadioGroup, RadioGroupItem } from "@ragtop-web/ui/components/radio-group";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@ragtop-web/ui/components/tabs";
import { useTranslations } from "next-intl";

// 支持的语言选项
const LANGUAGE_OPTIONS = [
  { value: "English", label: "English" },
  { value: "Chinese", label: "中文" },
];

interface AgentDetailsProps {
  agent: AgentDetailsParams;
}

/**
 * Agent详情组件，展示Agent的所有信息，包括助理设置、提示引擎和模型设置
 */
export function AgentDetails({ agent }: AgentDetailsProps) {
  const t = useTranslations("web.agent");

  // 辅助函数：获取agent名称（兼容不同接口）
  const getAgentName = () => {
    return (agent as Agents).name || (agent as AgentDetailsParams).name || t("common.untitled");
  };

  // 辅助函数：获取agent资源列表（兼容不同接口）
  const getAgentResources = () => {
    return (agent as AgentDetailsParams).link_resources?.map(({ res_name }) => res_name) || [];
  };

  // 辅助函数：获取agent可见性/权限（兼容不同接口）
  const getAgentScope = () => {
    const { scope } = agent as Agents;

    if (scope === "PRIVATE") {
      return Scope.Private;
    }
    if (scope === "TEAM_PUBLIC") {
      return Scope.TeamPublic;
    }

    return (agent as AgentDetailsParams).scope || Scope.Private;
  };

  // 辅助函数：获取已配置的语言
  const getConfiguredLanguages = () => {
    const languages = agent.language_settings?.cross_languages || [];

    return languages.map((lang: string) => {
      const option = LANGUAGE_OPTIONS.find((opt) => opt.value === lang);

      return option ? option.label : lang;
    });
  };

  return (
    <div className="space-y-6">
      {!agent.settings?.llm_model_name && agent.type === Type.Rag && (
        <p className="text-destructive">{t("common.modelNotConfigured")}</p>
      )}
      <Tabs defaultValue="assistant-settings" className="w-full">
        <TabsList className={`grid w-full grid-cols-3`}>
          <TabsTrigger value="assistant-settings">{t("tabs.basicSettings")}</TabsTrigger>
          <TabsTrigger value="prompt-engine">{t("tabs.promptEngine")}</TabsTrigger>
          <TabsTrigger value="model-settings">{t("tabs.modelSettings")}</TabsTrigger>
        </TabsList>

        {/* 助理设置标签页 */}
        <TabsContent value="assistant-settings" className="space-y-4 px-2 pb-2">
          <Card>
            <CardHeader>
              <CardTitle>{t("details.basicInfo")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 md:grid-cols-2">
                <div>
                  <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                    {t("details.agentName")}
                  </h3>
                  <p>{getAgentName()}</p>
                </div>
                <div>
                  <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                    {t("details.resourceType")}
                  </h3>
                  <p>
                    {agent.type === Type.Rag
                      ? t("details.resourceTypes.knowledgeBase")
                      : agent.type === Type.NLToSQL
                        ? t("details.resourceTypes.databaseDataset")
                        : t("details.resourceTypes.fileDataset")}
                  </p>
                </div>
                <div>
                  <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                    {agent.type === Type.Rag ? t("form.knowledgeBase") : t("form.datasets")}
                  </h3>
                  <p>{getAgentResources().join(", ") || t("details.none")}</p>
                </div>
                <div>
                  <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                    {t("details.agentPermission")}
                  </h3>
                  <RadioGroup value={getAgentScope()} className="flex space-x-6">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value={Scope.Private} id="private" disabled />
                      <label
                        htmlFor="private"
                        className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {t("form.personal")}
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value={Scope.TeamPublic} id="team" disabled />
                      <label
                        htmlFor="team"
                        className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {t("form.team")}
                      </label>
                    </div>
                  </RadioGroup>
                </div>

                <div>
                  <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                    {t("details.supportedLanguages")}
                  </h3>
                  <div className="flex flex-wrap gap-1">
                    {getConfiguredLanguages().length > 0 ? (
                      getConfiguredLanguages().map((lang, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {lang}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-muted-foreground text-sm">
                        {t("details.notConfigured")}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                  {t("details.agentDescription")}
                </h3>
                <p>{agent.description || t("details.noDescription")}</p>
              </div>
              <div>
                <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                  {t("details.greeting")}
                </h3>
                <p>{agent.hello_message}</p>
              </div>
              <div>
                <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                  {t("details.emptyReply")}
                </h3>
                <p>{agent.abnormal_message || t("details.notSet")}</p>
              </div>
              <div>
                <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                  {t("details.historyContext")}
                </h3>
                <p>{agent.history_num || 0}</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 提示引擎标签页 */}
        <TabsContent value="prompt-engine" className="space-y-4 px-2 pb-2">
          <Card>
            <CardHeader>
              <CardTitle>{t("details.promptSettings")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                  {t("details.systemPrompt")}
                </h3>
                <pre className="bg-muted rounded-md p-4 text-sm whitespace-pre-wrap">
                  {agent.prompt || t("details.notSetYet")}
                </pre>
              </div>
              {agent.type !== Type.Rag && (
                <div>
                  <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                    {t("details.summaryPrompt")}
                  </h3>
                  <pre className="bg-muted rounded-md p-4 text-sm whitespace-pre-wrap">
                    {agent.summary_prompt || t("details.notSetYet")}
                  </pre>
                </div>
              )}
            </CardContent>
          </Card>

          {agent.type === Type.Rag && (
            <Card>
              <CardHeader>
                <CardTitle>{t("details.retrievalSettings")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                      {t("details.similarityThreshold")}
                    </h3>
                    <p>{agent.settings?.similarity_threshold}</p>
                  </div>
                  <div>
                    <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                      {t("details.keywordWeight")}
                    </h3>
                    <p>{agent.settings?.keyword_weight}</p>
                  </div>
                  <div>
                    <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                      {t("details.retrievalCount")}
                    </h3>
                    <p>{agent.settings?.top_n}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* 模型设置标签页 - 仅在知识库类型时显示 */}
        <TabsContent value="model-settings" className="space-y-4 px-2 pb-2">
          <Card>
            <CardHeader>
              <CardTitle>{t("details.modelConfig")}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                    {t("details.model")}
                  </h3>
                  <p>{agent.settings?.llm_model_name}</p>
                </div>

                <div>
                  <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                    {t("details.temperature")}
                  </h3>
                  <p>{agent.settings?.temperature}</p>
                </div>
                <div>
                  <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                    {t("details.topP")}
                  </h3>
                  <p>{agent.settings?.top_p}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
