"use client";

/**
 * 语言检测和切换工具函数
 */

export type SupportedLocale = 'zh' | 'en';

/**
 * 检测浏览器默认语言
 */
export function detectBrowserLocale(): SupportedLocale {
  if (typeof window === 'undefined') {
    return 'zh'; // SSR 默认中文
  }

  // 获取浏览器语言
  const browserLang = navigator.language || navigator.languages?.[0] || 'zh';
  
  // 如果是中文或英文，使用浏览器语言，否则默认中文
  if (browserLang.startsWith('zh')) {
    return 'zh';
  }
  if (browserLang.startsWith('en')) {
    return 'en';
  }
  
  // 默认中文
  return 'zh';
}

/**
 * 从 localStorage 获取用户设置的语言
 */
export function getStoredLocale(): SupportedLocale | null {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    const stored = localStorage.getItem('locale');
    if (stored === 'zh' || stored === 'en') {
      return stored;
    }
  } catch (error) {
    console.warn('Failed to read locale from localStorage:', error);
  }

  return null;
}

/**
 * 保存用户设置的语言到 localStorage
 */
export function setStoredLocale(locale: SupportedLocale): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    localStorage.setItem('locale', locale);
  } catch (error) {
    console.warn('Failed to save locale to localStorage:', error);
  }
}

/**
 * 获取当前应该使用的语言
 * 优先级：用户设置 > 浏览器语言 > 默认中文
 */
export function getCurrentLocale(): SupportedLocale {
  // 1. 优先使用用户设置的语言
  const storedLocale = getStoredLocale();
  if (storedLocale) {
    return storedLocale;
  }

  // 2. 使用浏览器语言检测
  return detectBrowserLocale();
}

/**
 * 切换语言
 */
export function switchLocale(locale: SupportedLocale): void {
  // 保存到 localStorage
  setStoredLocale(locale);
  
  // 设置 HTML lang 属性（用于 packages/ui 的翻译检测）
  if (typeof window !== 'undefined') {
    document.documentElement.lang = locale;
  }
  
  // 刷新页面以应用新语言
  window.location.reload();
}

/**
 * 初始化语言设置
 * 在应用启动时调用，设置正确的 HTML lang 属性
 */
export function initializeLocale(): SupportedLocale {
  const locale = getCurrentLocale();
  
  if (typeof window !== 'undefined') {
    document.documentElement.lang = locale;
  }
  
  return locale;
}
