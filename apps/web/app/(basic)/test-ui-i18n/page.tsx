"use client";

import { But<PERSON> } from "@ragtop-web/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ragtop-web/ui/components/card";
import { ConfirmDialog } from "@ragtop-web/ui/components/confirm-dialog";
import { Pagination } from "@ragtop-web/ui/components/pagination";
import { useUiTranslations } from "@ragtop-web/ui/hooks/use-ui-translations";
import { useState } from "react";

/**
 * UI 组件国际化测试页面
 *
 * 用于测试 packages/ui 中组件的国际化功能
 */
export default function TestUiI18nPage() {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [currentLang, setCurrentLang] = useState(() =>
    typeof window !== "undefined" ? document.documentElement.lang || "en" : "en"
  );
  const { common, pagination } = useUiTranslations();

  // 模拟数据
  const totalItems = 156;
  const pageCount = Math.ceil(totalItems / pageSize);

  // 切换语言
  const switchLanguage = (lang: string) => {
    document.documentElement.lang = lang;
    setCurrentLang(lang);
  };

  return (
    <div className="container mx-auto space-y-8 p-6">
      <Card>
        <CardHeader>
          <CardTitle>UI 组件国际化测试</CardTitle>
          <div className="mt-4 flex gap-2">
            <Button
              variant={currentLang === "zh" ? "default" : "outline"}
              size="sm"
              onClick={() => switchLanguage("zh")}
            >
              中文
            </Button>
            <Button
              variant={currentLang === "en" ? "default" : "outline"}
              size="sm"
              onClick={() => switchLanguage("en")}
            >
              English
            </Button>
            <span className="text-muted-foreground ml-2 self-center text-sm">
              当前语言: {currentLang}
            </span>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 通用按钮测试 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">通用按钮文本测试</h3>
            <div className="flex flex-wrap gap-2">
              <Button variant="default">{common.save}</Button>
              <Button variant="outline">{common.cancel}</Button>
              <Button variant="destructive" onClick={() => setShowConfirmDialog(true)}>
                {common.delete}
              </Button>
              <Button variant="secondary">{common.edit}</Button>
              <Button variant="ghost">{common.add}</Button>
              <Button variant="link">{common.close}</Button>
            </div>
          </div>

          {/* 分页组件测试 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">分页组件测试</h3>
            <div className="space-y-4">
              <div className="text-muted-foreground text-sm">
                当前页: {currentPage}, 每页大小: {pageSize}, 总条目: {totalItems}
              </div>

              <Pagination
                pageCount={pageCount}
                currentPage={currentPage}
                onPageChange={setCurrentPage}
                total={totalItems}
                pageSize={pageSize}
                onPageSizeChange={setPageSize}
                showTotal={true}
                showPageSizeSelector={true}
              />
            </div>
          </div>

          {/* 翻译键直接访问测试 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">翻译键直接访问测试</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>分页翻译:</strong>
                <ul className="mt-2 list-inside list-disc space-y-1">
                  <li>上一页: {pagination.previous}</li>
                  <li>下一页: {pagination.next}</li>
                  <li>更多页面: {pagination.morePages}</li>
                  <li>每页: {pagination.itemsPerPage}</li>
                  <li>条: {pagination.items}</li>
                </ul>
              </div>
              <div>
                <strong>通用翻译:</strong>
                <ul className="mt-2 list-inside list-disc space-y-1">
                  <li>加载中: {common.loading}</li>
                  <li>错误: {common.error}</li>
                  <li>成功: {common.success}</li>
                  <li>确认: {common.confirm}</li>
                  <li>搜索: {common.search}</li>
                </ul>
              </div>
            </div>
          </div>

          {/* 使用说明 */}
          <div className="bg-muted/30 rounded-lg p-4">
            <h4 className="mb-2 font-semibold">使用说明</h4>
            <ul className="list-inside list-disc space-y-1 text-sm">
              <li>测试分页组件的翻译功能，包括"上一页"、"下一页"、"每页"等文本</li>
              <li>测试通用按钮的翻译功能</li>
              <li>可以通过修改 HTML lang 属性来测试不同语言的显示效果</li>
              <li>所有翻译文本都来自 packages/ui 内置的翻译字典</li>
              <li>组件会自动检测当前语言环境并显示相应的翻译</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* 确认对话框测试 */}
      <ConfirmDialog
        open={showConfirmDialog}
        onOpenChange={setShowConfirmDialog}
        title="确认删除"
        description="您确定要删除这个项目吗？此操作无法撤销。"
        onConfirm={() => {
          console.log("删除确认");
          setShowConfirmDialog(false);
        }}
        variant="destructive"
      />
    </div>
  );
}
