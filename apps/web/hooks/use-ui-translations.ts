"use client";

import { useTranslations } from "next-intl";
import { 
  PaginationTranslations, 
  CommonTranslations, 
  DialogTranslations 
} from "@ragtop-web/ui/hooks/use-ui-translations";

/**
 * Web 应用的 UI 组件国际化 hook
 * 
 * 将 next-intl 的翻译转换为 UI 组件所需的格式
 */
export function useUiTranslations() {
  const t = useTranslations("ui");
  
  const paginationTranslations: PaginationTranslations = {
    previous: t("pagination.previous"),
    next: t("pagination.next"),
    morePages: t("pagination.morePages"),
    showingItems: (params: { start: number; end: number; total: number }) =>
      t("pagination.showingItems", params),
    itemsPerPage: t("pagination.itemsPerPage"),
    items: t("pagination.items"),
  };
  
  const commonTranslations: CommonTranslations = {
    loading: t("common.loading"),
    error: t("common.error"),
    success: t("common.success"),
    cancel: t("common.cancel"),
    confirm: t("common.confirm"),
    save: t("common.save"),
    delete: t("common.delete"),
    edit: t("common.edit"),
    add: t("common.add"),
    search: t("common.search"),
    close: t("common.close"),
    processing: t("common.processing"),
  };
  
  const dialogTranslations: DialogTranslations = {
    close: t("dialog.close"),
  };
  
  return {
    pagination: paginationTranslations,
    common: commonTranslations,
    dialog: dialogTranslations,
  };
}

/**
 * 分页组件专用的翻译 hook
 */
export function usePaginationTranslations(): PaginationTranslations {
  const { pagination } = useUiTranslations();
  return pagination;
}

/**
 * 通用组件专用的翻译 hook
 */
export function useCommonTranslations(): CommonTranslations {
  const { common } = useUiTranslations();
  return common;
}

/**
 * 对话框组件专用的翻译 hook
 */
export function useDialogTranslations(): DialogTranslations {
  const { dialog } = useUiTranslations();
  return dialog;
}
