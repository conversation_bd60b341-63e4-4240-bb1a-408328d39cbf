"use client";

import { useLocale as useNextIntlLocale } from "next-intl";
import { useCallback } from "react";
import { SupportedLocale, switchLocale } from "@/lib/locale-utils";

/**
 * 语言切换 hook
 */
export function useLocale() {
  const currentLocale = useNextIntlLocale() as SupportedLocale;

  const changeLocale = useCallback((locale: SupportedLocale) => {
    if (locale !== currentLocale) {
      // 设置 cookie
      document.cookie = `locale=${locale}; path=/; max-age=${60 * 60 * 24 * 365}`; // 1年
      
      // 切换语言
      switchLocale(locale);
    }
  }, [currentLocale]);

  return {
    currentLocale,
    changeLocale,
    isZh: currentLocale === 'zh',
    isEn: currentLocale === 'en',
  };
}
