import { getRequestConfig } from "next-intl/server";
import { cookies, headers } from "next/headers";

export default getRequestConfig(async () => {
  // 1. 优先从 cookie 获取用户设置的语言
  const cookieStore = await cookies();
  const cookieLocale = cookieStore.get("locale")?.value;

  if (cookieLocale === "zh" || cookieLocale === "en") {
    return {
      locale: cookieLocale,
      messages: (await import(`@ragtop-web/i18n/dictionaries/${cookieLocale}.json`)).default,
    };
  }

  // 2. 从请求头获取浏览器语言偏好
  const headersList = await headers();
  const acceptLanguage = headersList.get("accept-language") || "";

  let locale: "zh" | "en" = "zh"; // 默认中文

  // 解析 Accept-Language 头
  if (acceptLanguage) {
    const languages = acceptLanguage
      .split(",")
      .map((lang) => lang.split(";")[0]?.trim().toLowerCase() || "");

    // 检查是否包含中文或英文
    for (const lang of languages) {
      if (lang.startsWith("zh")) {
        locale = "zh";
        break;
      }
      if (lang.startsWith("en")) {
        locale = "en";
        break;
      }
    }
  }

  return {
    locale,
    messages: (await import(`@ragtop-web/i18n/dictionaries/${locale}.json`)).default,
  };
});
